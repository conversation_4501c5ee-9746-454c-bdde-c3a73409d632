"use client"

import { ReplyCard, type Reply } from "./reply-card"
import { usePagination } from "./use-pagination"
import { PaginationControls } from "./pagination-controls"

interface RepliesSectionProps {
  replies: Reply[]
  onReplyEdit?: (reply: Reply) => void
}

export function RepliesSection({ replies, onReplyEdit }: RepliesSectionProps) {
  const {
    currentPage,
    setCurrentPage,
    paginatedData: paginatedReplies,
    totalPages,
    pageNumbers
  } = usePagination({ data: replies, itemsPerPage: 4 })

  return (
    <div className="h-full flex flex-col space-y-4">
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 flex-1 overflow-y-auto pr-2">
        {paginatedReplies.map((reply) => (
          <ReplyCard 
            key={reply.id} 
            reply={reply} 
            onEdit={onReplyEdit}
          />
        ))}
      </div>
      <PaginationControls
        currentPage={currentPage}
        totalPages={totalPages}
        pageNumbers={pageNumbers}
        onPageChange={setCurrentPage}
      />
    </div>
  )
}
