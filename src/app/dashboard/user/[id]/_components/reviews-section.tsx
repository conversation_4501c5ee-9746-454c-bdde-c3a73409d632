"use client"

import { ReviewCard, type Review } from "./review-card"
import { usePagination } from "./use-pagination"
import { PaginationControls } from "./pagination-controls"

interface ReviewsSectionProps {
  reviews: Review[]
  onReviewEdit?: (review: Review) => void
}

export function ReviewsSection({ reviews, onReviewEdit }: ReviewsSectionProps) {
  const {
    currentPage,
    setCurrentPage,
    paginatedData: paginatedReviews,
    totalPages,
    pageNumbers
  } = usePagination({ data: reviews, itemsPerPage: 4 })

  return (
    <div className="h-full flex flex-col space-y-4">
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 flex-1 overflow-y-auto pr-2">
        {paginatedReviews.map((review) => (
          <ReviewCard 
            key={review.id} 
            review={review} 
            onEdit={onReviewEdit}
          />
        ))}
      </div>
      <PaginationControls
        currentPage={currentPage}
        totalPages={totalPages}
        pageNumbers={pageNumbers}
        onPageChange={setCurrentPage}
      />
    </div>
  )
}
